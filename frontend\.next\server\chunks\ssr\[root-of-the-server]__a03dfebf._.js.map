{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/i18n.ts"], "sourcesContent": ["export type Locale = 'ar' | 'fr' | 'en';\n\nexport const locales: Locale[] = ['ar', 'fr', 'en'];\n\nexport const defaultLocale: Locale = 'ar';\n\nexport const localeNames = {\n  ar: 'العربية',\n  fr: 'Français', \n  en: 'English'\n};\n\nexport const localeFlags = {\n  ar: '🇲🇦',\n  fr: '🇫🇷',\n  en: '🇬🇧'\n};\n\nexport const rtlLocales: Locale[] = ['ar'];\n\nexport function isRtlLocale(locale: Locale): boolean {\n  return rtlLocales.includes(locale);\n}\n"], "names": [], "mappings": ";;;;;;;;AAEO,MAAM,UAAoB;IAAC;IAAM;IAAM;CAAK;AAE5C,MAAM,gBAAwB;AAE9B,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,MAAM,aAAuB;IAAC;CAAK;AAEnC,SAAS,YAAY,MAAc;IACxC,OAAO,WAAW,QAAQ,CAAC;AAC7B", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/hooks/useTranslation.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react';\nimport { Locale, defaultLocale } from '@/lib/i18n';\n\n// Import translation files\nimport arTranslations from '@/locales/ar.json';\nimport frTranslations from '@/locales/fr.json';\nimport enTranslations from '@/locales/en.json';\n\nconst translations = {\n  ar: arTranslations,\n  fr: frTranslations,\n  en: enTranslations,\n};\n\nexport function useTranslation() {\n  const [locale, setLocale] = useState<Locale>(defaultLocale);\n\n  useEffect(() => {\n    // Get locale from localStorage or use default\n    const savedLocale = localStorage.getItem('locale') as Locale;\n    if (savedLocale && ['ar', 'fr', 'en'].includes(savedLocale)) {\n      setLocale(savedLocale);\n    }\n  }, []);\n\n  const changeLocale = (newLocale: Locale) => {\n    setLocale(newLocale);\n    localStorage.setItem('locale', newLocale);\n    \n    // Update document direction and language\n    document.documentElement.lang = newLocale;\n    document.documentElement.dir = newLocale === 'ar' ? 'rtl' : 'ltr';\n  };\n\n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[locale];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n\n  return {\n    locale,\n    changeLocale,\n    t,\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA,2BAA2B;AAC3B;AACA;AACA;AARA;;;;;;AAUA,MAAM,eAAe;IACnB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;IAClB,IAAI,4FAAA,CAAA,UAAc;AACpB;AAEO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,kHAAA,CAAA,gBAAa;IAE1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,MAAM,cAAc,aAAa,OAAO,CAAC;QACzC,IAAI,eAAe;YAAC;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,cAAc;YAC3D,UAAU;QACZ;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,aAAa,OAAO,CAAC,UAAU;QAE/B,yCAAyC;QACzC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,cAAc,OAAO,QAAQ;IAC9D;IAEA,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,OAAO;QAErC,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\n\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/language-toggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Languages, Check, Globe } from \"lucide-react\"\nimport { useTranslation } from \"@/hooks/useTranslation\"\nimport { Locale, localeNames, localeFlags } from \"@/lib/i18n\"\n\nimport { Button } from \"@/components/ui/button\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n} from \"@/components/ui/dropdown-menu\"\n\nexport function LanguageToggle() {\n  const { locale, changeLocale } = useTranslation()\n\n  const getCurrentLanguageInfo = () => {\n    return {\n      flag: localeFlags[locale],\n      name: localeNames[locale],\n      code: locale.toUpperCase()\n    }\n  }\n\n  const currentLang = getCurrentLanguageInfo()\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"h-9 px-3 gap-2 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 group\"\n        >\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-lg group-hover:scale-110 transition-transform duration-300\">\n              {currentLang.flag}\n            </span>\n            <span className=\"hidden sm:inline-block text-sm font-medium\">\n              {currentLang.code}\n            </span>\n          </div>\n          <Globe className=\"h-4 w-4 opacity-60 group-hover:opacity-100 transition-opacity duration-300\" />\n          <span className=\"sr-only\">تغيير اللغة / Change language</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent\n        align=\"end\"\n        className=\"w-48 p-2\"\n        sideOffset={8}\n      >\n        <DropdownMenuLabel className=\"text-xs font-medium text-gray-500 dark:text-gray-400 px-2 py-1\">\n          {locale === 'ar' ? 'اختر اللغة' : locale === 'fr' ? 'Choisir la langue' : 'Choose Language'}\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        {Object.entries(localeNames).map(([code, name]) => (\n          <DropdownMenuItem\n            key={code}\n            onClick={() => changeLocale(code as Locale)}\n            className={`flex items-center gap-3 px-3 py-2.5 rounded-lg cursor-pointer transition-all duration-200 ${\n              locale === code\n                ? \"bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300\"\n                : \"hover:bg-gray-100 dark:hover:bg-gray-700\"\n            }`}\n          >\n            <span className=\"text-lg\">{localeFlags[code as Locale]}</span>\n            <div className=\"flex-1\">\n              <div className=\"font-medium text-sm\">{name}</div>\n              <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                {code === 'ar' ? 'العربية' : code === 'fr' ? 'Français' : 'English'}\n              </div>\n            </div>\n            {locale === code && (\n              <Check className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n            )}\n          </DropdownMenuItem>\n        ))}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAEA;AACA;AARA;;;;;;;AAiBO,SAAS;IACd,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE9C,MAAM,yBAAyB;QAC7B,OAAO;YACL,MAAM,kHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,kHAAA,CAAA,cAAW,CAAC,OAAO;YACzB,MAAM,OAAO,WAAW;QAC1B;IACF;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CACb,YAAY,IAAI;;;;;;;;;;;;sCAGrB,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAEZ,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC1B,WAAW,OAAO,eAAe,WAAW,OAAO,sBAAsB;;;;;;kCAE5E,8OAAC,4IAAA,CAAA,wBAAqB;;;;;oBACrB,OAAO,OAAO,CAAC,kHAAA,CAAA,cAAW,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,iBAC5C,8OAAC,4IAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,0FAA0F,EACpG,WAAW,OACP,qEACA,4CACJ;;8CAEF,8OAAC;oCAAK,WAAU;8CAAW,kHAAA,CAAA,cAAW,CAAC,KAAe;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuB;;;;;;sDACtC,8OAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;;;;;;;;;;;;gCAG7D,WAAW,sBACV,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;2BAhBd;;;;;;;;;;;;;;;;;AAuBjB", "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/auth/UserMenu.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useAuth, UserRole } from '@/contexts/AuthContext'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Button } from '@/components/ui/button'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport { User, Settings, LogOut, Shield, School, Truck, GraduationCap } from 'lucide-react'\nimport Link from 'next/link'\n\nexport function UserMenu() {\n  const { user, profile, signOut } = useAuth()\n  const { t } = useTranslation()\n\n  if (!user || !profile) {\n    return (\n      <Button variant=\"outline\" asChild>\n        <a href=\"/auth\">{t('auth.login')}</a>\n      </Button>\n    )\n  }\n\n  const getRoleIcon = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return <Shield className=\"h-4 w-4\" />\n      case UserRole.SCHOOL:\n        return <School className=\"h-4 w-4\" />\n      case UserRole.DELIVERY:\n        return <Truck className=\"h-4 w-4\" />\n      case UserRole.STUDENT:\n        return <GraduationCap className=\"h-4 w-4\" />\n      default:\n        return <User className=\"h-4 w-4\" />\n    }\n  }\n\n  const getRoleLabel = (role: UserRole) => {\n    switch (role) {\n      case UserRole.ADMIN:\n        return 'مدير'\n      case UserRole.SCHOOL:\n        return 'مدرسة'\n      case UserRole.DELIVERY:\n        return 'شريك توصيل'\n      case UserRole.STUDENT:\n        return 'طالب'\n      default:\n        return 'مستخدم'\n    }\n  }\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n    // إعادة توجيه إلى الصفحة الرئيسية بعد تسجيل الخروج\n    window.location.href = '/'\n  }\n\n  const getDashboardUrl = () => {\n    if (!profile) return '/dashboard'\n\n    // توجيه المدير إلى لوحة تحكم الإدارة\n    if (profile.role === UserRole.ADMIN) {\n      return '/dashboard/admin'\n    }\n\n    // باقي الأدوار إلى لوحة التحكم العامة\n    return '/dashboard'\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"outline\" size=\"icon\">\n          <User className=\"h-[1.2rem] w-[1.2rem]\" />\n          <span className=\"sr-only\">User menu</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n        <DropdownMenuLabel className=\"font-normal\">\n          <div className=\"flex flex-col space-y-1\">\n            <p className=\"text-sm font-medium leading-none\">\n              {profile.full_name}\n            </p>\n            <p className=\"text-xs leading-none text-muted-foreground\">\n              {user.email}\n            </p>\n            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n              {getRoleIcon(profile.role)}\n              <span>{getRoleLabel(profile.role)}</span>\n            </div>\n          </div>\n        </DropdownMenuLabel>\n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem asChild>\n          <Link href=\"/profile\" className=\"cursor-pointer flex items-center\">\n            <User className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.profile')}</span>\n          </Link>\n        </DropdownMenuItem>\n\n        <DropdownMenuItem asChild>\n          <Link href={getDashboardUrl()} className=\"cursor-pointer flex items-center\">\n            <Settings className=\"mr-2 h-4 w-4\" />\n            <span>{t('navigation.dashboard')}</span>\n          </Link>\n        </DropdownMenuItem>\n        \n        <DropdownMenuSeparator />\n        \n        <DropdownMenuItem \n          className=\"cursor-pointer text-red-600 focus:text-red-600\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-2 h-4 w-4\" />\n          <span>{t('auth.logout')}</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAfA;;;;;;;;AAiBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3B,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,SAAQ;YAAU,OAAO;sBAC/B,cAAA,8OAAC;gBAAE,MAAK;0BAAS,EAAE;;;;;;;;;;;IAGzB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK,+HAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,+HAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK,+HAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK,+HAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK,+HAAA,CAAA,WAAQ,CAAC,KAAK;gBACjB,OAAO;YACT,KAAK,+HAAA,CAAA,WAAQ,CAAC,MAAM;gBAClB,OAAO;YACT,KAAK,+HAAA,CAAA,WAAQ,CAAC,QAAQ;gBACpB,OAAO;YACT,KAAK,+HAAA,CAAA,WAAQ,CAAC,OAAO;gBACnB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,mDAAmD;QACnD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO;QAErB,qCAAqC;QACrC,IAAI,QAAQ,IAAI,KAAK,+HAAA,CAAA,WAAQ,CAAC,KAAK,EAAE;YACnC,OAAO;QACT;QAEA,sCAAsC;QACtC,OAAO;IACT;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,WAAU;gBAAO,OAAM;gBAAM,UAAU;;kCAC1D,8OAAC,4IAAA,CAAA,oBAAiB;wBAAC,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,QAAQ,SAAS;;;;;;8CAEpB,8OAAC;oCAAE,WAAU;8CACV,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCAAI,WAAU;;wCACZ,YAAY,QAAQ,IAAI;sDACzB,8OAAC;sDAAM,aAAa,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;kCAItC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;;8CAC9B,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,OAAO;kCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM;4BAAmB,WAAU;;8CACvC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;kCAIb,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kCAEtB,8OAAC,4IAAA,CAAA,mBAAgB;wBACf,WAAU;wBACV,SAAS;;0CAET,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/notifications/NotificationDropdown.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from 'react'\nimport { useNotifications, getNotificationIcon, getNotificationColor, formatNotificationTime } from '@/contexts/NotificationContext'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Separator } from '@/components/ui/separator'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { \n  Bell,\n  Check,\n  CheckCheck,\n  Trash2,\n  ExternalLink,\n  Settings,\n  X\n} from 'lucide-react'\n\nexport function NotificationDropdown() {\n  const { \n    notifications, \n    unreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification, \n    clearAll \n  } = useNotifications()\n  \n  const [isOpen, setIsOpen] = useState(false)\n\n  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {\n    markAsRead(notificationId)\n    if (actionUrl) {\n      window.location.href = actionUrl\n    }\n  }\n\n  const recentNotifications = notifications.slice(0, 10)\n\n  return (\n    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n          <Bell className=\"h-5 w-5\" />\n          {unreadCount > 0 && (\n            <Badge \n              variant=\"destructive\" \n              className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n            >\n              {unreadCount > 99 ? '99+' : unreadCount}\n            </Badge>\n          )}\n        </Button>\n      </DropdownMenuTrigger>\n      \n      <DropdownMenuContent \n        align=\"end\" \n        className=\"w-80 p-0\"\n        sideOffset={5}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b\">\n          <h3 className=\"font-semibold arabic-text\">الإشعارات</h3>\n          <div className=\"flex items-center gap-2\">\n            {unreadCount > 0 && (\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={markAllAsRead}\n                className=\"h-8 px-2 text-xs arabic-text\"\n              >\n                <CheckCheck className=\"h-3 w-3 mr-1\" />\n                قراءة الكل\n              </Button>\n            )}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-8 w-8 p-0\"\n            >\n              <Settings className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n\n        {/* Notifications List */}\n        <ScrollArea className=\"h-96\">\n          {recentNotifications.length === 0 ? (\n            <div className=\"flex flex-col items-center justify-center py-8 text-center\">\n              <Bell className=\"h-12 w-12 text-gray-400 mb-3\" />\n              <p className=\"text-gray-500 arabic-text\">لا توجد إشعارات</p>\n              <p className=\"text-sm text-gray-400 arabic-text\">ستظهر إشعاراتك هنا</p>\n            </div>\n          ) : (\n            <div className=\"divide-y\">\n              {recentNotifications.map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer ${\n                    !notification.isRead ? 'bg-blue-50/50 dark:bg-blue-900/10' : ''\n                  }`}\n                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* Icon */}\n                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm ${\n                      getNotificationColor(notification.priority)\n                    }`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between\">\n                        <h4 className={`text-sm font-medium arabic-text ${\n                          !notification.isRead ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        \n                        {/* Actions */}\n                        <div className=\"flex items-center gap-1 ml-2\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                          )}\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-100 hover:text-red-600\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              removeNotification(notification.id)\n                            }}\n                          >\n                            <X className=\"h-3 w-3\" />\n                          </Button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 arabic-text line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500\">\n                          {formatNotificationTime(notification.createdAt)}\n                        </span>\n\n                        {notification.actionText && notification.actionUrl && (\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"h-6 px-2 text-xs text-blue-600 hover:text-blue-700 arabic-text\"\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              handleNotificationClick(notification.id, notification.actionUrl)\n                            }}\n                          >\n                            {notification.actionText}\n                            <ExternalLink className=\"h-3 w-3 mr-1\" />\n                          </Button>\n                        )}\n                      </div>\n\n                      {/* Expiry Warning */}\n                      {notification.expiresAt && (\n                        <div className=\"mt-2\">\n                          <Badge variant=\"outline\" className=\"text-xs arabic-text\">\n                            ينتهي في {formatNotificationTime(notification.expiresAt)}\n                          </Badge>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </ScrollArea>\n\n        {/* Footer */}\n        {notifications.length > 0 && (\n          <>\n            <Separator />\n            <div className=\"p-3 flex items-center justify-between\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"text-xs arabic-text\"\n                asChild\n              >\n                <a href=\"/notifications\">عرض جميع الإشعارات</a>\n              </Button>\n              \n              {notifications.length > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAll}\n                  className=\"text-xs text-red-600 hover:text-red-700 arabic-text\"\n                >\n                  <Trash2 className=\"h-3 w-3 mr-1\" />\n                  حذف الكل\n                </Button>\n              )}\n            </div>\n          </>\n        )}\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n\n// مكون إشعار منبثق للإشعارات الهامة\ninterface ToastNotificationProps {\n  notification: {\n    id: string\n    title: string\n    message: string\n    type: string\n    priority: string\n  }\n  onClose: () => void\n  onAction?: () => void\n}\n\nexport function ToastNotification({ notification, onClose, onAction }: ToastNotificationProps) {\n  return (\n    <div className={`fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border ${\n      getNotificationColor(notification.priority as any)\n    } animate-in slide-in-from-right duration-300`}>\n      <div className=\"flex items-start gap-3\">\n        <div className=\"flex-shrink-0 text-lg\">\n          {getNotificationIcon(notification.type as any)}\n        </div>\n        \n        <div className=\"flex-1\">\n          <h4 className=\"font-medium arabic-text\">{notification.title}</h4>\n          <p className=\"text-sm mt-1 arabic-text\">{notification.message}</p>\n          \n          <div className=\"flex items-center gap-2 mt-3\">\n            {onAction && (\n              <Button size=\"sm\" onClick={onAction} className=\"arabic-text\">\n                عرض\n              </Button>\n            )}\n            <Button variant=\"ghost\" size=\"sm\" onClick={onClose}>\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// مكون عداد الإشعارات البسيط\nexport function NotificationBadge() {\n  const { unreadCount } = useNotifications()\n\n  if (unreadCount === 0) return null\n\n  return (\n    <Badge \n      variant=\"destructive\" \n      className=\"absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs\"\n    >\n      {unreadCount > 99 ? '99+' : unreadCount}\n    </Badge>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;AAuBO,SAAS;IACd,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,QAAQ,EACT,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,WAAW;QACX,IAAI,WAAW;YACb,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,sBAAsB,cAAc,KAAK,CAAC,GAAG;IAEnD,qBACE,8OAAC,4IAAA,CAAA,eAAY;QAAC,MAAM;QAAQ,cAAc;;0BACxC,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,WAAU;;sCAC1C,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,cAAc,mBACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,SAAQ;4BACR,WAAU;sCAET,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;0BAMpC,8OAAC,4IAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;gBACV,YAAY;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;;oCACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAI3C,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,8OAAC,0IAAA,CAAA,aAAU;wBAAC,WAAU;kCACnB,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;8CAA4B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;iDAGnD,8OAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,6BACxB,8OAAC;oCAEC,WAAW,CAAC,6EAA6E,EACvF,CAAC,aAAa,MAAM,GAAG,sCAAsC,IAC7D;oCACF,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;8CAE9E,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAW,CAAC,4EAA4E,EAC3F,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,GAC1C;0DACC,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;0DAIxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAW,CAAC,gCAAgC,EAC9C,CAAC,aAAa,MAAM,GAAG,kCAAkC,oCACzD;0EACC,aAAa,KAAK;;;;;;0EAIrB,8OAAC;gEAAI,WAAU;;oEACZ,CAAC,aAAa,MAAM,kBACnB,8OAAC;wEAAI,WAAU;;;;;;kFAEjB,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,CAAC;4EACR,EAAE,eAAe;4EACjB,mBAAmB,aAAa,EAAE;wEACpC;kFAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKnB,8OAAC;wDAAE,WAAU;kEACV,aAAa,OAAO;;;;;;kEAGvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;4DAG/C,aAAa,UAAU,IAAI,aAAa,SAAS,kBAChD,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,wBAAwB,aAAa,EAAE,EAAE,aAAa,SAAS;gEACjE;;oEAEC,aAAa,UAAU;kFACxB,8OAAC,sNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;oDAM7B,aAAa,SAAS,kBACrB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAsB;gEAC7C,CAAA,GAAA,uIAAA,CAAA,yBAAsB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mCAvE5D,aAAa,EAAE;;;;;;;;;;;;;;;oBAoF7B,cAAc,MAAM,GAAG,mBACtB;;0CACE,8OAAC,qIAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAE,MAAK;sDAAiB;;;;;;;;;;;oCAG1B,cAAc,MAAM,GAAG,mBACtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;AAeO,SAAS,kBAAkB,EAAE,YAAY,EAAE,OAAO,EAAE,QAAQ,EAA0B;IAC3F,qBACE,8OAAC;QAAI,WAAW,CAAC,8DAA8D,EAC7E,CAAA,GAAA,uIAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,QAAQ,EAC3C,4CAA4C,CAAC;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI;;;;;;8BAGxC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2B,aAAa,KAAK;;;;;;sCAC3D,8OAAC;4BAAE,WAAU;sCAA4B,aAAa,OAAO;;;;;;sCAE7D,8OAAC;4BAAI,WAAU;;gCACZ,0BACC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAS;oCAAU,WAAU;8CAAc;;;;;;8CAI/D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;AAGO,SAAS;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD;IAEvC,IAAI,gBAAgB,GAAG,OAAO;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAU;kBAET,cAAc,KAAK,QAAQ;;;;;;AAGlC", "debugId": null}}, {"offset": {"line": 1598, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Button } from '@/components/ui/button'\nimport { ThemeToggle } from '@/components/theme-toggle'\nimport { LanguageToggle } from '@/components/language-toggle'\nimport { UserMenu } from '@/components/auth/UserMenu'\nimport { NotificationDropdown } from '@/components/notifications/NotificationDropdown'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  GraduationCap,\n  Home,\n  ShoppingBag,\n  Palette,\n  Info,\n  Phone,\n  Search,\n  Heart,\n  ExternalLink,\n  FileText,\n  Link as LinkIcon\n} from 'lucide-react'\n\n// أنواع البيانات لعناصر القائمة\ninterface MenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n}\n\nexport function Navigation() {\n  const { t, locale } = useTranslation()\n  const pathname = usePathname()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [cartItemsCount, setCartItemsCount] = useState(0)\n  const [wishlistCount, setWishlistCount] = useState(0)\n  const [menuItems, setMenuItems] = useState<MenuItem[]>([])\n  const [loading, setLoading] = useState(true)\n\n  // Close mobile menu when route changes\n  useEffect(() => {\n    setIsMobileMenuOpen(false)\n  }, [pathname])\n\n  // Mock cart and wishlist counts - replace with actual data\n  useEffect(() => {\n    // Simulate getting cart items from localStorage or API\n    const cartItems = JSON.parse(localStorage.getItem('cartItems') || '[]')\n    setCartItemsCount(cartItems.length)\n\n    const wishlistItems = JSON.parse(localStorage.getItem('wishlistItems') || '[]')\n    setWishlistCount(wishlistItems.length)\n  }, [])\n\n  // جلب عناصر القائمة من قاعدة البيانات\n  useEffect(() => {\n    const fetchMenuItems = async () => {\n      try {\n        setLoading(true)\n        const response = await fetch('/api/menu-items?parent_id=null')\n\n        if (response.ok) {\n          const data = await response.json()\n          setMenuItems(data.menuItems || [])\n        } else {\n          // في حالة فشل API، استخدم القائمة الافتراضية\n          console.warn('Failed to fetch menu items from API, using default menu')\n          setMenuItems([])\n        }\n      } catch (error) {\n        // في حالة خطأ في الشبكة، استخدم القائمة الافتراضية\n        console.warn('Error fetching menu items, using default menu:', error)\n        setMenuItems([])\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    fetchMenuItems()\n  }, [])\n\n  // تحويل عناصر القائمة من قاعدة البيانات إلى تنسيق المكون\n  const getNavItemsFromDB = () => {\n    return menuItems.map(item => {\n      // اختيار العنوان حسب اللغة الحالية\n      let label = item.title_ar // افتراضي\n      if (locale === 'en' && item.title_en) {\n        label = item.title_en\n      } else if (locale === 'fr' && item.title_fr) {\n        label = item.title_fr\n      }\n\n      // تحديد الرابط حسب نوع الهدف\n      let href = item.target_value\n      if (item.target_type === 'page') {\n        href = `/pages/${item.target_value}`\n      }\n\n      // تحديد الأيقونة\n      let icon = <LinkIcon className=\"h-4 w-4\" />\n      if (item.icon) {\n        // يمكن إضافة منطق لتحويل اسم الأيقونة إلى مكون\n        switch (item.icon) {\n          case 'Home':\n            icon = <Home className=\"h-4 w-4\" />\n            break\n          case 'ShoppingBag':\n            icon = <ShoppingBag className=\"h-4 w-4\" />\n            break\n          case 'Palette':\n            icon = <Palette className=\"h-4 w-4\" />\n            break\n          case 'Search':\n            icon = <Search className=\"h-4 w-4\" />\n            break\n          case 'Info':\n            icon = <Info className=\"h-4 w-4\" />\n            break\n          case 'Phone':\n            icon = <Phone className=\"h-4 w-4\" />\n            break\n          case 'ExternalLink':\n            icon = <ExternalLink className=\"h-4 w-4\" />\n            break\n          case 'FileText':\n            icon = <FileText className=\"h-4 w-4\" />\n            break\n          default:\n            icon = <LinkIcon className=\"h-4 w-4\" />\n        }\n      }\n\n      return {\n        href,\n        label,\n        icon,\n        target_type: item.target_type\n      }\n    })\n  }\n\n  // القائمة الافتراضية (للاستخدام في حالة عدم وجود عناصر من قاعدة البيانات)\n  const defaultNavItems = [\n    {\n      href: '/',\n      label: t('navigation.home'),\n      icon: <Home className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/catalog',\n      label: t('navigation.catalog'),\n      icon: <ShoppingBag className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/customize',\n      label: t('navigation.customize'),\n      icon: <Palette className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/track-order',\n      label: t('navigation.trackOrder') || 'تتبع الطلب',\n      icon: <Search className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/about',\n      label: t('navigation.about') || 'من نحن',\n      icon: <Info className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    },\n    {\n      href: '/contact',\n      label: t('navigation.contact') || 'تواصل معنا',\n      icon: <Phone className=\"h-4 w-4\" />,\n      target_type: 'internal' as const\n    }\n  ]\n\n  // استخدام عناصر القائمة من قاعدة البيانات أو الافتراضية\n  const navItems = loading ? defaultNavItems : (menuItems.length > 0 ? getNavItemsFromDB() : defaultNavItems)\n\n  // استخدام عناصر القائمة فقط (بدون لوحة التحكم في القائمة الرئيسية)\n  const { user, profile } = useAuth()\n  const allNavItems = navItems\n\n  const isActive = (href: string) => {\n    if (href === '/') {\n      return pathname === '/'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <header className=\"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 transition-all duration-300\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-3\">\n          {/* Logo */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center gap-3 hover:opacity-80 transition-all duration-300 group\"\n          >\n            <div className=\"relative\">\n              <GraduationCap className=\"h-9 w-9 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-300\" />\n              <div className=\"absolute -inset-1 bg-blue-600/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-gray-900 dark:text-white leading-tight\">\n                Graduation Toqs\n              </span>\n              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                {locale === 'ar' ? 'منصة أزياء التخرج' : locale === 'fr' ? 'Plateforme de Remise des Diplômes' : 'Graduation Platform'}\n              </span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center gap-1\">\n            {allNavItems.map((item) => {\n              // تحديد ما إذا كان الرابط خارجي\n              const isExternal = item.target_type === 'external'\n              const linkProps = isExternal\n                ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                : { href: item.href }\n\n              return (\n                <Link\n                  key={item.href}\n                  {...linkProps}\n                  className={`group relative flex items-center gap-2 px-4 py-2.5 rounded-xl font-medium transition-all duration-300 ${\n                    isActive(item.href)\n                      ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                  }`}\n                >\n                  <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : 'group-hover:scale-110'}`}>\n                    {item.icon}\n                  </span>\n                  <span className=\"text-sm font-medium\">\n                    {item.label}\n                  </span>\n                  {isExternal && (\n                    <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                  )}\n                  {isActive(item.href) && (\n                    <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\"></div>\n                  )}\n                </Link>\n              )\n            })}\n          </nav>\n\n          {/* Desktop Actions */}\n          <div className=\"hidden lg:flex items-center gap-2\">\n            {/* Wishlist */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/wishlist\">\n                <Heart className=\"h-5 w-5 transition-colors group-hover:text-red-500\" />\n                {wishlistCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs animate-pulse\"\n                  >\n                    {wishlistCount > 99 ? '99+' : wishlistCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Cart Icon */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative group\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5 transition-colors group-hover:text-blue-600\" />\n                {cartItemsCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 hover:bg-blue-700 animate-pulse\"\n                  >\n                    {cartItemsCount > 99 ? '99+' : cartItemsCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Notifications */}\n            <NotificationDropdown />\n\n            {/* Divider */}\n            <div className=\"h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n\n            {/* Language & Theme Controls */}\n            <div className=\"flex items-center gap-1\">\n              <LanguageToggle />\n              <ThemeToggle />\n            </div>\n\n            {/* User Menu */}\n            <UserMenu />\n          </div>\n\n          {/* Mobile Actions */}\n          <div className=\"flex lg:hidden items-center gap-2\">\n            {/* Mobile Cart */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n              <Link href=\"/cart\">\n                <ShoppingBag className=\"h-5 w-5\" />\n                {cartItemsCount > 0 && (\n                  <Badge\n                    className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs bg-blue-600\"\n                  >\n                    {cartItemsCount > 9 ? '9+' : cartItemsCount}\n                  </Badge>\n                )}\n              </Link>\n            </Button>\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"relative\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? 'rotate-45' : '-translate-y-1.5'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transition-all duration-300 ${\n                    isMobileMenuOpen ? 'opacity-0' : 'opacity-100'\n                  }`}\n                />\n                <span\n                  className={`absolute h-0.5 w-6 bg-current transform transition-all duration-300 ${\n                    isMobileMenuOpen ? '-rotate-45' : 'translate-y-1.5'\n                  }`}\n                />\n              </div>\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div\n          className={`lg:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <div className=\"border-t border-gray-200 dark:border-gray-700 py-4\">\n            <nav className=\"flex flex-col gap-1 mb-6\">\n              {allNavItems.map((item, index) => {\n                // تحديد ما إذا كان الرابط خارجي\n                const isExternal = item.target_type === 'external'\n                const linkProps = isExternal\n                  ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }\n                  : { href: item.href }\n\n                return (\n                  <Link\n                    key={item.href}\n                    {...linkProps}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                    className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 font-medium ${\n                      isActive(item.href)\n                        ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'\n                        : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'\n                    }`}\n                    style={{\n                      animationName: isMobileMenuOpen ? 'slideInFromRight' : 'none',\n                      animationDuration: '0.3s',\n                      animationTimingFunction: 'ease-out',\n                      animationFillMode: 'forwards',\n                      animationDelay: `${index * 50}ms`\n                    }}\n                  >\n                    <span className={`transition-transform duration-300 ${isActive(item.href) ? 'scale-110' : ''}`}>\n                      {item.icon}\n                    </span>\n                    <span className=\"text-sm\">\n                      {item.label}\n                    </span>\n                    {isExternal && (\n                      <ExternalLink className=\"h-3 w-3 opacity-60\" />\n                    )}\n                  </Link>\n                )\n              })}\n            </nav>\n\n            {/* Mobile Actions */}\n            <div className=\"flex items-center justify-between px-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center gap-2\">\n                <LanguageToggle />\n                <ThemeToggle />\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"relative\" asChild>\n                  <Link href=\"/wishlist\">\n                    <Heart className=\"h-5 w-5\" />\n                    {wishlistCount > 0 && (\n                      <Badge\n                        variant=\"destructive\"\n                        className=\"absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-xs\"\n                      >\n                        {wishlistCount > 9 ? '9+' : wishlistCount}\n                      </Badge>\n                    )}\n                  </Link>\n                </Button>\n                <UserMenu />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Navigation\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;;;;;AA0CO,SAAS;IACd,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;IACtB,GAAG;QAAC;KAAS;IAEb,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uDAAuD;QACvD,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,gBAAgB;QAClE,kBAAkB,UAAU,MAAM;QAElC,MAAM,gBAAgB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,oBAAoB;QAC1E,iBAAiB,cAAc,MAAM;IACvC,GAAG,EAAE;IAEL,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,aAAa,KAAK,SAAS,IAAI,EAAE;gBACnC,OAAO;oBACL,6CAA6C;oBAC7C,QAAQ,IAAI,CAAC;oBACb,aAAa,EAAE;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,mDAAmD;gBACnD,QAAQ,IAAI,CAAC,kDAAkD;gBAC/D,aAAa,EAAE;YACjB,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,oBAAoB;QACxB,OAAO,UAAU,GAAG,CAAC,CAAA;YACnB,mCAAmC;YACnC,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU;;YACpC,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBACpC,QAAQ,KAAK,QAAQ;YACvB,OAAO,IAAI,WAAW,QAAQ,KAAK,QAAQ,EAAE;gBAC3C,QAAQ,KAAK,QAAQ;YACvB;YAEA,6BAA6B;YAC7B,IAAI,OAAO,KAAK,YAAY;YAC5B,IAAI,KAAK,WAAW,KAAK,QAAQ;gBAC/B,OAAO,CAAC,OAAO,EAAE,KAAK,YAAY,EAAE;YACtC;YAEA,iBAAiB;YACjB,IAAI,qBAAO,8OAAC,kMAAA,CAAA,OAAQ;gBAAC,WAAU;;;;;;YAC/B,IAAI,KAAK,IAAI,EAAE;gBACb,+CAA+C;gBAC/C,OAAQ,KAAK,IAAI;oBACf,KAAK;wBACH,qBAAO,8OAAC,mMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAC9B;oBACF,KAAK;wBACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAC1B;oBACF,KAAK;wBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACzB;oBACF,KAAK;wBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACvB;oBACF,KAAK;wBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBACxB;oBACF,KAAK;wBACH,qBAAO,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC/B;oBACF,KAAK;wBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAC3B;oBACF;wBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAQ;4BAAC,WAAU;;;;;;gBAC/B;YACF;YAEA,OAAO;gBACL;gBACA;gBACA;gBACA,aAAa,KAAK,WAAW;YAC/B;QACF;IACF;IAEA,0EAA0E;IAC1E,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,4BAA4B;YACrC,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,uBAAuB;YAChC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO,EAAE,yBAAyB;YAClC,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;QACf;KACD;IAED,wDAAwD;IACxD,MAAM,WAAW,UAAU,kBAAmB,UAAU,MAAM,GAAG,IAAI,sBAAsB;IAE3F,mEAAmE;IACnE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,cAAc;IAEpB,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAgE;;;;;;sDAGhF,8OAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,sBAAsB,WAAW,OAAO,sCAAsC;;;;;;;;;;;;;;;;;;sCAMvG,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,gCAAgC;gCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;gCACxC,MAAM,YAAY,aACd;oCAAE,MAAM,KAAK,IAAI;oCAAE,QAAQ;oCAAU,KAAK;gCAAsB,IAChE;oCAAE,MAAM,KAAK,IAAI;gCAAC;gCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEF,GAAG,SAAS;oCACb,WAAW,CAAC,sGAAsG,EAChH,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;;sDAEF,8OAAC;4CAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,yBAAyB;sDAChH,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;wCAEZ,4BACC,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAEzB,SAAS,KAAK,IAAI,mBACjB,8OAAC;4CAAI,WAAU;;;;;;;mCAlBZ,KAAK,IAAI;;;;;4BAsBpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,gBAAgB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOtC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAiB,OAAO;8CAClE,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,iBAAiB,mBAChB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,iBAAiB,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAOvC,8OAAC,2JAAA,CAAA,uBAAoB;;;;;8CAGrB,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;sDACf,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;8CAId,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;sCAIX,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;oCAAW,OAAO;8CAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,iBAAiB,mBAChB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAU;0DAET,iBAAiB,IAAI,OAAO;;;;;;;;;;;;;;;;;8CAOrC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEpC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,cAAc,oBACjC;;;;;;0DAEJ,8OAAC;gDACC,WAAW,CAAC,0DAA0D,EACpE,mBAAmB,cAAc,eACjC;;;;;;0DAEJ,8OAAC;gDACC,WAAW,CAAC,oEAAoE,EAC9E,mBAAmB,eAAe,mBAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQZ,8OAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,mBAAmB,6BAA6B,qBAChD;8BAEF,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,gCAAgC;oCAChC,MAAM,aAAa,KAAK,WAAW,KAAK;oCACxC,MAAM,YAAY,aACd;wCAAE,MAAM,KAAK,IAAI;wCAAE,QAAQ;wCAAU,KAAK;oCAAsB,IAChE;wCAAE,MAAM,KAAK,IAAI;oCAAC;oCAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAEF,GAAG,SAAS;wCACb,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,0FAA0F,EACpG,SAAS,KAAK,IAAI,IACd,wDACA,4HACJ;wCACF,OAAO;4CACL,eAAe,mBAAmB,qBAAqB;4CACvD,mBAAmB;4CACnB,yBAAyB;4CACzB,mBAAmB;4CACnB,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wCACnC;;0DAEA,8OAAC;gDAAK,WAAW,CAAC,kCAAkC,EAAE,SAAS,KAAK,IAAI,IAAI,cAAc,IAAI;0DAC3F,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;4CAEZ,4BACC,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAvBrB,KAAK,IAAI;;;;;gCA2BpB;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wIAAA,CAAA,iBAAc;;;;;0DACf,8OAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;gDAAW,OAAO;0DAC5D,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEAET,gBAAgB,IAAI,OAAO;;;;;;;;;;;;;;;;;0DAKpC,8OAAC,sIAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;uCAEe", "debugId": null}}, {"offset": {"line": 2386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>rad<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Users } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Navigation } from \"@/components/Navigation\";\nimport { useTranslation } from \"@/hooks/useTranslation\";\n\nexport default function Home() {\n  const { t } = useTranslation();\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900\">\n      <Navigation />\n\n      {/* Hero Section */}\n      <main className=\"container mx-auto px-4 py-12\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-5xl font-bold text-gray-900 dark:text-white mb-6 arabic-text\">\n            🎓 {t('home.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto arabic-text leading-relaxed\">\n            {t('home.subtitle')}\n          </p>\n          <div className=\"flex gap-4 justify-center\">\n            <Button size=\"lg\" className=\"bg-blue-600 hover:bg-blue-700 arabic-text\" asChild>\n              <a href=\"/customize\">{t('home.startCustomizing')}</a>\n            </Button>\n            <Button variant=\"outline\" size=\"lg\" className=\"arabic-text\" asChild>\n              <a href=\"/catalog\">{t('home.browseCatalog')}</a>\n            </Button>\n          </div>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\n          <Card className=\"text-center\">\n            <CardHeader>\n              <Palette className=\"h-12 w-12 text-purple-600 mx-auto mb-4\" />\n              <CardTitle className=\"arabic-text\">{t('home.features.customization.title')}</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"arabic-text\">\n                {t('home.features.customization.description')}\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card className=\"text-center\">\n            <CardHeader>\n              <Sparkles className=\"h-12 w-12 text-yellow-600 mx-auto mb-4\" />\n              <CardTitle className=\"arabic-text\">{t('home.features.ai.title')}</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"arabic-text\">\n                {t('home.features.ai.description')}\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card className=\"text-center\">\n            <CardHeader>\n              <Users className=\"h-12 w-12 text-green-600 mx-auto mb-4\" />\n              <CardTitle className=\"arabic-text\">{t('home.features.roles.title')}</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"arabic-text\">\n                {t('home.features.roles.description')}\n              </CardDescription>\n            </CardContent>\n          </Card>\n\n          <Card className=\"text-center\">\n            <CardHeader>\n              <GraduationCap className=\"h-12 w-12 text-blue-600 mx-auto mb-4\" />\n              <CardTitle className=\"arabic-text\">{t('home.features.tracking.title')}</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <CardDescription className=\"arabic-text\">\n                {t('home.features.tracking.description')}\n              </CardDescription>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Language Support */}\n        <div className=\"text-center\">\n          <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4 arabic-text\">\n            {t('home.languageSupport')}\n          </h3>\n          <div className=\"flex justify-center gap-8 text-lg\">\n            <span className=\"flex items-center gap-2\">\n              🇲🇦 العربية\n            </span>\n            <span className=\"flex items-center gap-2\">\n              🇫🇷 Français\n            </span>\n            <span className=\"flex items-center gap-2\">\n              🇬🇧 English\n            </span>\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-100 dark:bg-gray-800 py-8 mt-16\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <p className=\"text-gray-600 dark:text-gray-300 arabic-text\">\n            {t('home.footer')}\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAE3B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BAGX,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoE;oCAC5E,EAAE;;;;;;;0CAER,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;wCAA4C,OAAO;kDAC7E,cAAA,8OAAC;4CAAE,MAAK;sDAAc,EAAE;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;wCAAc,OAAO;kDACjE,cAAA,8OAAC;4CAAE,MAAK;sDAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;kCAM5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAe,EAAE;;;;;;;;;;;;kDAExC,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,EAAE;;;;;;;;;;;;;;;;;0CAKT,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAe,EAAE;;;;;;;;;;;;kDAExC,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,EAAE;;;;;;;;;;;;;;;;;0CAKT,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAe,EAAE;;;;;;;;;;;;kDAExC,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,EAAE;;;;;;;;;;;;;;;;;0CAKT,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAe,EAAE;;;;;;;;;;;;kDAExC,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDACxB,EAAE;;;;;;;;;;;;;;;;;;;;;;;kCAOX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;kDAG1C,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;kDAG1C,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}