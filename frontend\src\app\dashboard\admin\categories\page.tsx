"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Plus, 
  Edit, 
  Trash2, 
  MoreHorizontal,
  Package,
  Eye,
  EyeOff,
  ArrowUp,
  ArrowDown
} from 'lucide-react'

// أنواع البيانات
interface Category {
  id: string
  name_ar: string
  name_en?: string
  name_fr?: string
  slug: string
  icon?: string
  description?: string
  is_active: boolean
  order_index: number
  created_at: string
  updated_at: string
}

interface CategoryFormData {
  name_ar: string
  name_en: string
  name_fr: string
  slug: string
  icon: string
  description: string
  is_active: boolean
  order_index: number
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddCategory, setShowAddCategory] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [formData, setFormData] = useState<CategoryFormData>({
    name_ar: '',
    name_en: '',
    name_fr: '',
    slug: '',
    icon: '',
    description: '',
    is_active: true,
    order_index: 1
  })

  // جلب الفئات
  const fetchCategories = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/categories?include_inactive=true')
      if (response.ok) {
        const data = await response.json()
        setCategories(data.categories)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      name_ar: '',
      name_en: '',
      name_fr: '',
      slug: '',
      icon: '',
      description: '',
      is_active: true,
      order_index: categories.length + 1
    })
    setEditingCategory(null)
  }

  // تحضير النموذج للتعديل
  const prepareEdit = (category: Category) => {
    setFormData({
      name_ar: category.name_ar,
      name_en: category.name_en || '',
      name_fr: category.name_fr || '',
      slug: category.slug,
      icon: category.icon || '',
      description: category.description || '',
      is_active: category.is_active,
      order_index: category.order_index
    })
    setEditingCategory(category)
    setShowAddCategory(true)
  }

  // إنشاء slug تلقائياً من الاسم العربي
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[أإآ]/g, 'a')
      .replace(/[ة]/g, 'h')
      .replace(/[ى]/g, 'y')
      .replace(/[ء]/g, '')
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]/g, '')
  }

  // معالجة تغيير الاسم العربي
  const handleNameChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      name_ar: value,
      slug: prev.slug || generateSlug(value)
    }))
  }

  // حفظ الفئة
  const handleSave = async () => {
    try {
      const url = editingCategory 
        ? `/api/categories/${editingCategory.id}`
        : '/api/categories'
      
      const method = editingCategory ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في حفظ الفئة')
      }

      await fetchCategories()
      setShowAddCategory(false)
      resetForm()
      alert(editingCategory ? 'تم تحديث الفئة بنجاح!' : 'تم إضافة الفئة بنجاح!')
    } catch (error) {
      console.error('Error saving category:', error)
      alert(error instanceof Error ? error.message : 'فشل في حفظ الفئة')
    }
  }

  // حذف فئة
  const handleDelete = async (categoryId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
      return
    }

    try {
      const response = await fetch(`/api/categories/${categoryId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في حذف الفئة')
      }

      await fetchCategories()
      alert('تم حذف الفئة بنجاح!')
    } catch (error) {
      console.error('Error deleting category:', error)
      alert(error instanceof Error ? error.message : 'فشل في حذف الفئة')
    }
  }

  // تبديل حالة الفئة
  const toggleCategoryStatus = async (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId)
    if (!category) return

    try {
      const response = await fetch(`/api/categories/${categoryId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          is_active: !category.is_active
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'فشل في تحديث حالة الفئة')
      }

      await fetchCategories()
    } catch (error) {
      console.error('Error toggling category status:', error)
      alert(error instanceof Error ? error.message : 'فشل في تحديث حالة الفئة')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 arabic-text">جاري تحميل الفئات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
                إدارة الفئات 📂
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
                إضافة وتعديل وإدارة فئات المنتجات
              </p>
            </div>
            <Dialog open={showAddCategory} onOpenChange={setShowAddCategory}>
              <DialogTrigger asChild>
                <Button onClick={resetForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  إضافة فئة جديدة
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="arabic-text">
                    {editingCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
                  </DialogTitle>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name_ar" className="arabic-text">الاسم بالعربية *</Label>
                      <Input
                        id="name_ar"
                        value={formData.name_ar}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="أدخل اسم الفئة بالعربية"
                        className="arabic-text"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="name_en">الاسم بالإنجليزية</Label>
                      <Input
                        id="name_en"
                        value={formData.name_en}
                        onChange={(e) => setFormData(prev => ({ ...prev, name_en: e.target.value }))}
                        placeholder="Enter category name in English"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="slug" className="arabic-text">الرابط المختصر *</Label>
                      <Input
                        id="slug"
                        value={formData.slug}
                        onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                        placeholder="category-slug"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="icon" className="arabic-text">الأيقونة</Label>
                      <Input
                        id="icon"
                        value={formData.icon}
                        onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
                        placeholder="🎓"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description" className="arabic-text">الوصف</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="وصف الفئة..."
                      className="arabic-text"
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_active"
                      checked={formData.is_active}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                    />
                    <Label htmlFor="is_active" className="arabic-text">فئة نشطة</Label>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowAddCategory(false)}>
                      إلغاء
                    </Button>
                    <Button onClick={handleSave}>
                      {editingCategory ? 'تحديث' : 'إضافة'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Categories Table */}
        <Card>
          <CardHeader>
            <CardTitle className="arabic-text">قائمة الفئات</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="arabic-text">الاسم</TableHead>
                  <TableHead className="arabic-text">الرابط المختصر</TableHead>
                  <TableHead className="arabic-text">الحالة</TableHead>
                  <TableHead className="arabic-text">الترتيب</TableHead>
                  <TableHead className="arabic-text">تاريخ الإنشاء</TableHead>
                  <TableHead className="arabic-text">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categories.map((category) => (
                  <TableRow key={category.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {category.icon && <span className="text-lg">{category.icon}</span>}
                        <div>
                          <div className="font-medium arabic-text">{category.name_ar}</div>
                          {category.name_en && (
                            <div className="text-sm text-gray-500">{category.name_en}</div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm">
                        {category.slug}
                      </code>
                    </TableCell>
                    <TableCell>
                      <Badge variant={category.is_active ? "default" : "secondary"}>
                        {category.is_active ? 'نشط' : 'غير نشط'}
                      </Badge>
                    </TableCell>
                    <TableCell>{category.order_index}</TableCell>
                    <TableCell>
                      {new Date(category.created_at).toLocaleDateString('ar-SA')}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => prepareEdit(category)}>
                            <Edit className="h-4 w-4 mr-2" />
                            تعديل
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => toggleCategoryStatus(category.id)}>
                            {category.is_active ? (
                              <>
                                <EyeOff className="h-4 w-4 mr-2" />
                                إلغاء التفعيل
                              </>
                            ) : (
                              <>
                                <Eye className="h-4 w-4 mr-2" />
                                تفعيل
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(category.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            حذف
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
