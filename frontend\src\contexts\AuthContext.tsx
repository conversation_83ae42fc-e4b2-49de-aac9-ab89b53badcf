"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'

// أنواع البيانات للمصادقة
export enum UserRole {
  STUDENT = 'student',
  SCHOOL = 'school',
  ADMIN = 'admin',
  DELIVERY = 'delivery'
}

export interface UserProfile {
  id: string
  email: string
  full_name: string
  role: UserRole
  phone?: string
  school_name?: string
  created_at: string
  updated_at: string
}

// نوع مبسط للمستخدم للتطوير
interface User {
  id: string
  email?: string
}

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  loading: boolean
  signUp: (email: string, password: string, userData: {
    full_name: string
    role: UserRole
    phone?: string
    school_name?: string
  }) => Promise<{ data: unknown, error: string | null }>
  signIn: (email: string, password: string) => Promise<{ data: unknown, error: string | null }>
  signOut: () => Promise<{ error: string | null }>
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ data: unknown, error: string | null }>
  hasRole: (requiredRole: UserRole) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(false) // تعطيل التحميل للتطوير

  useEffect(() => {
    // للتطوير - استرجاع المستخدم من localStorage
    const savedUser = localStorage.getItem('mockUser')
    const savedProfile = localStorage.getItem('mockProfile')

    if (savedUser && savedProfile) {
      setUser(JSON.parse(savedUser))
      setProfile(JSON.parse(savedProfile))
    }

    setLoading(false)
  }, [])

  const signUp = async (email: string, password: string, userData: {
    full_name: string
    role: UserRole
    phone?: string
    school_name?: string
  }) => {
    // للتطوير - محاكاة تسجيل حساب جديد
    console.log('Sign up:', email, userData)
    return { data: { user: { id: '1', email } }, error: null }
  }

  const signIn = async (email: string, password: string) => {
    // للتطوير - محاكاة تسجيل الدخول
    console.log('Sign in:', email)
    const mockUser = { id: '1', email }

    // تحديد الدور بناءً على الإيميل للتطوير
    let role = UserRole.STUDENT
    if (email.includes('admin')) {
      role = UserRole.ADMIN
    } else if (email.includes('school')) {
      role = UserRole.SCHOOL
    } else if (email.includes('delivery')) {
      role = UserRole.DELIVERY
    }

    const mockProfile: UserProfile = {
      id: '1',
      email,
      full_name: email.split('@')[0] || 'مستخدم',
      role,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    setUser(mockUser)
    setProfile(mockProfile)

    // حفظ في localStorage للتطوير
    localStorage.setItem('mockUser', JSON.stringify(mockUser))
    localStorage.setItem('mockProfile', JSON.stringify(mockProfile))

    // إعادة التوجيه بناءً على الدور
    setTimeout(() => {
      if (role === UserRole.ADMIN) {
        window.location.href = '/dashboard/admin'
      } else if (role === UserRole.SCHOOL) {
        window.location.href = '/dashboard/school'
      } else if (role === UserRole.DELIVERY) {
        window.location.href = '/dashboard/delivery'
      } else {
        window.location.href = '/dashboard/student'
      }
    }, 100)

    return { data: { user: mockUser }, error: null }
  }

  const signOut = async () => {
    setUser(null)
    setProfile(null)

    // حذف من localStorage
    localStorage.removeItem('mockUser')
    localStorage.removeItem('mockProfile')

    return { error: null }
  }

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) return { data: null, error: 'No user logged in' }

    const updatedProfile = { ...profile, ...updates } as UserProfile
    setProfile(updatedProfile)
    return { data: updatedProfile, error: null }
  }

  const hasRole = (requiredRole: UserRole): boolean => {
    if (!profile) return false

    // نظام هرمي بسيط للأدوار
    const roleHierarchy = {
      [UserRole.ADMIN]: 4,
      [UserRole.SCHOOL]: 3,
      [UserRole.DELIVERY]: 2,
      [UserRole.STUDENT]: 1
    }

    return roleHierarchy[profile.role] >= roleHierarchy[requiredRole]
  }

  const value = {
    user,
    profile,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
    hasRole
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
