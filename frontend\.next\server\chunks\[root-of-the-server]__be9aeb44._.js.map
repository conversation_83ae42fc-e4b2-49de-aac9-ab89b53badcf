{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport function createClient(cookieStore: ReturnType<typeof cookies>) {\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        get(name: string) {\n          return cookieStore.get(name)?.value\n        },\n        set(name: string, value: string, options: any) {\n          try {\n            cookieStore.set({ name, value, ...options })\n          } catch (error) {\n            // The `set` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n        remove(name: string, options: any) {\n          try {\n            cookieStore.set({ name, value: '', ...options })\n          } catch (error) {\n            // The `delete` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGO,SAAS,aAAa,WAAuC;IAClE,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP,KAAI,IAAY;gBACd,OAAO,YAAY,GAAG,CAAC,OAAO;YAChC;YACA,KAAI,IAAY,EAAE,KAAa,EAAE,OAAY;gBAC3C,IAAI;oBACF,YAAY,GAAG,CAAC;wBAAE;wBAAM;wBAAO,GAAG,OAAO;oBAAC;gBAC5C,EAAE,OAAO,OAAO;gBACd,uDAAuD;gBACvD,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;YACA,QAAO,IAAY,EAAE,OAAY;gBAC/B,IAAI;oBACF,YAAY,GAAG,CAAC;wBAAE;wBAAM,OAAO;wBAAI,GAAG,OAAO;oBAAC;gBAChD,EAAE,OAAO,OAAO;gBACd,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\nimport { cookies } from 'next/headers'\n\n// POST - رفع الصور\nexport async function POST(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    // التحقق من صلاحيات المستخدم\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بالوصول' },\n        { status: 401 }\n      )\n    }\n\n    // التحقق من دور المستخدم\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (!profile || !['admin', 'school'].includes(profile.role)) {\n      return NextResponse.json(\n        { error: 'غير مصرح لك برفع الصور' },\n        { status: 403 }\n      )\n    }\n\n    const formData = await request.formData()\n    const files = formData.getAll('files') as File[]\n    const folder = formData.get('folder') as string || 'products'\n\n    if (!files || files.length === 0) {\n      return NextResponse.json(\n        { error: 'لم يتم اختيار أي ملفات' },\n        { status: 400 }\n      )\n    }\n\n    const uploadedFiles: { name: string; url: string; path: string }[] = []\n    const errors: string[] = []\n\n    for (const file of files) {\n      try {\n        // التحقق من نوع الملف\n        if (!file.type.startsWith('image/')) {\n          errors.push(`${file.name}: نوع الملف غير مدعوم`)\n          continue\n        }\n\n        // التحقق من حجم الملف (5MB)\n        if (file.size > 5 * 1024 * 1024) {\n          errors.push(`${file.name}: حجم الملف كبير جداً (أكثر من 5 ميجابايت)`)\n          continue\n        }\n\n        // إنشاء اسم فريد للملف\n        const fileExtension = file.name.split('.').pop()\n        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`\n        const filePath = `${folder}/${fileName}`\n\n        // تحويل الملف إلى ArrayBuffer\n        const arrayBuffer = await file.arrayBuffer()\n        const fileBuffer = new Uint8Array(arrayBuffer)\n\n        // رفع الملف إلى Supabase Storage\n        const { data: uploadData, error: uploadError } = await supabase.storage\n          .from('images')\n          .upload(filePath, fileBuffer, {\n            contentType: file.type,\n            cacheControl: '3600',\n            upsert: false\n          })\n\n        if (uploadError) {\n          console.error('Upload error:', uploadError)\n          errors.push(`${file.name}: فشل في رفع الملف`)\n          continue\n        }\n\n        // الحصول على رابط الملف العام\n        const { data: urlData } = supabase.storage\n          .from('images')\n          .getPublicUrl(filePath)\n\n        uploadedFiles.push({\n          name: file.name,\n          url: urlData.publicUrl,\n          path: filePath\n        })\n\n      } catch (error) {\n        console.error('Error processing file:', error)\n        errors.push(`${file.name}: خطأ في معالجة الملف`)\n      }\n    }\n\n    return NextResponse.json({\n      message: `تم رفع ${uploadedFiles.length} من ${files.length} ملف بنجاح`,\n      uploadedFiles,\n      errors: errors.length > 0 ? errors : undefined\n    })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE - حذف صورة\nexport async function DELETE(request: NextRequest) {\n  try {\n    const cookieStore = cookies()\n    const supabase = createClient(cookieStore)\n\n    // التحقق من صلاحيات المستخدم\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بالوصول' },\n        { status: 401 }\n      )\n    }\n\n    // التحقق من دور المستخدم\n    const { data: profile } = await supabase\n      .from('profiles')\n      .select('role')\n      .eq('id', user.id)\n      .single()\n\n    if (!profile || !['admin', 'school'].includes(profile.role)) {\n      return NextResponse.json(\n        { error: 'غير مصرح لك بحذف الصور' },\n        { status: 403 }\n      )\n    }\n\n    const { searchParams } = new URL(request.url)\n    const filePath = searchParams.get('path')\n\n    if (!filePath) {\n      return NextResponse.json(\n        { error: 'مسار الملف مطلوب' },\n        { status: 400 }\n      )\n    }\n\n    // حذف الملف من Supabase Storage\n    const { error: deleteError } = await supabase.storage\n      .from('images')\n      .remove([filePath])\n\n    if (deleteError) {\n      console.error('Delete error:', deleteError)\n      return NextResponse.json(\n        { error: 'فشل في حذف الملف' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({\n      message: 'تم حذف الملف بنجاح'\n    })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,6BAA6B;QAC7B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,CAAC,WAAW,CAAC;YAAC;YAAS;SAAS,CAAC,QAAQ,CAAC,QAAQ,IAAI,GAAG;YAC3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,QAAQ,SAAS,MAAM,CAAC;QAC9B,MAAM,SAAS,SAAS,GAAG,CAAC,aAAuB;QAEnD,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,gBAA+D,EAAE;QACvE,MAAM,SAAmB,EAAE;QAE3B,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;gBACF,sBAAsB;gBACtB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBACnC,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,qBAAqB,CAAC;oBAC/C;gBACF;gBAEA,4BAA4B;gBAC5B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;oBAC/B,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,0CAA0C,CAAC;oBACpE;gBACF;gBAEA,uBAAuB;gBACvB,MAAM,gBAAgB,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;gBAC9C,MAAM,WAAW,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE,eAAe;gBAC5F,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE,UAAU;gBAExC,8BAA8B;gBAC9B,MAAM,cAAc,MAAM,KAAK,WAAW;gBAC1C,MAAM,aAAa,IAAI,WAAW;gBAElC,iCAAiC;gBACjC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CACpE,IAAI,CAAC,UACL,MAAM,CAAC,UAAU,YAAY;oBAC5B,aAAa,KAAK,IAAI;oBACtB,cAAc;oBACd,QAAQ;gBACV;gBAEF,IAAI,aAAa;oBACf,QAAQ,KAAK,CAAC,iBAAiB;oBAC/B,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,kBAAkB,CAAC;oBAC5C;gBACF;gBAEA,8BAA8B;gBAC9B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,SAAS,OAAO,CACvC,IAAI,CAAC,UACL,YAAY,CAAC;gBAEhB,cAAc,IAAI,CAAC;oBACjB,MAAM,KAAK,IAAI;oBACf,KAAK,QAAQ,SAAS;oBACtB,MAAM;gBACR;YAEF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,qBAAqB,CAAC;YACjD;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,CAAC,OAAO,EAAE,cAAc,MAAM,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,UAAU,CAAC;YACtE;YACA,QAAQ,OAAO,MAAM,GAAG,IAAI,SAAS;QACvC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;QAC1B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD,EAAE;QAE9B,6BAA6B;QAC7B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,QACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,CAAC,WAAW,CAAC;YAAC;YAAS;SAAS,CAAC,QAAQ,CAAC,QAAQ,IAAI,GAAG;YAC3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAElC,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CAClD,IAAI,CAAC,UACL,MAAM,CAAC;YAAC;SAAS;QAEpB,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}