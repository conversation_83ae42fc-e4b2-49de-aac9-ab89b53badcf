{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/health/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\n// GET - فحص حالة الخادم\nexport async function GET() {\n  return NextResponse.json({ status: 'ok', timestamp: new Date().toISOString() })\n}\n\n// HEAD - فحص سريع للاتصال\nexport async function HEAD() {\n  return new NextResponse(null, { status: 200 })\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,QAAQ;QAAM,WAAW,IAAI,OAAO,WAAW;IAAG;AAC/E;AAGO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAAE,QAAQ;IAAI;AAC9C", "debugId": null}}]}