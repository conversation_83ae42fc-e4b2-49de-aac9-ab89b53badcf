{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/lib/mockData.ts"], "sourcesContent": ["// بيانات وهمية للتطوير والاختبار\nexport interface MockPage {\n  id: string\n  slug: string\n  is_published: boolean\n  author_id: string\n  featured_image?: string\n  created_at: string\n  updated_at: string\n  page_content: MockPageContent[]\n  profiles?: {\n    full_name: string\n  }\n}\n\nexport interface MockPageContent {\n  id: string\n  page_id: string\n  language: 'ar' | 'en' | 'fr'\n  title: string\n  content: string\n  meta_description?: string\n  meta_keywords?: string\n}\n\nexport interface MockMenuItem {\n  id: string\n  title_ar: string\n  title_en?: string\n  title_fr?: string\n  slug: string\n  icon?: string\n  parent_id?: string\n  order_index: number\n  is_active: boolean\n  target_type: 'internal' | 'external' | 'page'\n  target_value: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockCategory {\n  id: string\n  name_ar: string\n  name_en?: string\n  name_fr?: string\n  slug: string\n  icon?: string\n  description?: string\n  is_active: boolean\n  order_index: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface MockProduct {\n  id: string\n  name: string\n  description: string\n  category: string // تغيير من union type إلى string للمرونة\n  price: number\n  rental_price?: number\n  colors: string[]\n  sizes: string[]\n  images: string[]\n  stock_quantity: number\n  is_available: boolean\n  created_at: string\n  updated_at: string\n  rating?: number\n  reviews_count?: number\n  features?: string[]\n  specifications?: Record<string, any>\n}\n\n// بيانات وهمية للصفحات\nexport const mockPages: MockPage[] = [\n  {\n    id: '1',\n    slug: 'about-us',\n    is_published: true,\n    author_id: 'admin-1',\n    featured_image: '/images/about-hero.jpg',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '1-ar',\n        page_id: '1',\n        language: 'ar',\n        title: 'من نحن',\n        content: '<h2>مرحباً بكم في منصة أزياء التخرج</h2><p>نحن منصة متخصصة في تأجير وبيع أزياء التخرج المغربية الأصيلة. نهدف إلى جعل يوم تخرجكم لا يُنسى من خلال توفير أجمل الأزياء التقليدية.</p><p>تأسست منصتنا عام 2024 بهدف خدمة الطلاب والطالبات في جميع أنحاء المغرب، ونفتخر بتقديم خدمات عالية الجودة وأسعار مناسبة.</p>',\n        meta_description: 'تعرف على منصة أزياء التخرج المغربية - خدمات تأجير وبيع الأزياء التقليدية',\n        meta_keywords: 'أزياء التخرج، المغرب، تأجير، بيع، تقليدي'\n      },\n      {\n        id: '1-en',\n        page_id: '1',\n        language: 'en',\n        title: 'About Us',\n        content: '<h2>Welcome to Graduation Attire Platform</h2><p>We are a specialized platform for renting and selling authentic Moroccan graduation attire. We aim to make your graduation day unforgettable by providing the most beautiful traditional outfits.</p><p>Our platform was founded in 2024 to serve students throughout Morocco, and we pride ourselves on providing high-quality services at affordable prices.</p>',\n        meta_description: 'Learn about the Moroccan Graduation Attire Platform - traditional outfit rental and sales services',\n        meta_keywords: 'graduation attire, Morocco, rental, sales, traditional'\n      }\n    ]\n  },\n  {\n    id: '2',\n    slug: 'services',\n    is_published: true,\n    author_id: 'admin-1',\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '2-ar',\n        page_id: '2',\n        language: 'ar',\n        title: 'خدماتنا',\n        content: '<h2>خدماتنا المتميزة</h2><h3>تأجير الأزياء</h3><p>نوفر خدمة تأجير أزياء التخرج لفترات مرنة مع ضمان النظافة والجودة.</p><h3>بيع الأزياء</h3><p>إمكانية شراء الأزياء للاحتفاظ بها كذكرى جميلة من يوم التخرج.</p><h3>التخصيص</h3><p>خدمات تخصيص الأزياء حسب المقاسات والتفضيلات الشخصية.</p>',\n        meta_description: 'خدمات منصة أزياء التخرج - تأجير وبيع وتخصيص الأزياء التقليدية المغربية',\n        meta_keywords: 'خدمات، تأجير، بيع، تخصيص، أزياء التخرج'\n      }\n    ]\n  },\n  {\n    id: '3',\n    slug: 'contact',\n    is_published: false,\n    author_id: 'admin-1',\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-17T11:00:00Z',\n    profiles: {\n      full_name: 'مدير النظام'\n    },\n    page_content: [\n      {\n        id: '3-ar',\n        page_id: '3',\n        language: 'ar',\n        title: 'اتصل بنا',\n        content: '<h2>تواصل معنا</h2><p>نحن هنا لخدمتكم في أي وقت. يمكنكم التواصل معنا عبر:</p><ul><li>الهاتف: +212 5XX-XXXXXX</li><li>البريد الإلكتروني: <EMAIL></li><li>العنوان: الدار البيضاء، المغرب</li></ul>',\n        meta_description: 'تواصل مع منصة أزياء التخرج المغربية',\n        meta_keywords: 'اتصال، تواصل، خدمة العملاء'\n      }\n    ]\n  }\n]\n\n// بيانات وهمية للقوائم\nexport const mockMenuItems: MockMenuItem[] = [\n  {\n    id: '1',\n    title_ar: 'الرئيسية',\n    title_en: 'Home',\n    title_fr: 'Accueil',\n    slug: 'home',\n    icon: 'Home',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/',\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    title_ar: 'من نحن',\n    title_en: 'About Us',\n    title_fr: 'À propos',\n    slug: 'about',\n    icon: 'Info',\n    order_index: 2,\n    is_active: true,\n    target_type: 'page',\n    target_value: '1',\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    title_ar: 'خدماتنا',\n    title_en: 'Services',\n    title_fr: 'Services',\n    slug: 'services',\n    icon: 'Settings',\n    order_index: 3,\n    is_active: true,\n    target_type: 'page',\n    target_value: '2',\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    title_ar: 'المنتجات',\n    title_en: 'Products',\n    title_fr: 'Produits',\n    slug: 'products',\n    icon: 'Package',\n    order_index: 4,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products',\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    title_ar: 'تأجير الأزياء',\n    title_en: 'Rental',\n    title_fr: 'Location',\n    slug: 'rental',\n    parent_id: '4',\n    icon: 'Calendar',\n    order_index: 1,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=rental',\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  },\n  {\n    id: '6',\n    title_ar: 'بيع الأزياء',\n    title_en: 'Sales',\n    title_fr: 'Vente',\n    slug: 'sales',\n    parent_id: '4',\n    icon: 'ShoppingCart',\n    order_index: 2,\n    is_active: true,\n    target_type: 'internal',\n    target_value: '/products?type=sale',\n    created_at: '2024-01-15T10:25:00Z',\n    updated_at: '2024-01-15T10:25:00Z'\n  },\n  {\n    id: '7',\n    title_ar: 'اتصل بنا',\n    title_en: 'Contact',\n    title_fr: 'Contact',\n    slug: 'contact',\n    icon: 'Phone',\n    order_index: 5,\n    is_active: false,\n    target_type: 'page',\n    target_value: '3',\n    created_at: '2024-01-15T10:30:00Z',\n    updated_at: '2024-01-15T10:30:00Z'\n  }\n]\n\n// بيانات وهمية للفئات\nexport const mockCategories: MockCategory[] = [\n  {\n    id: '1',\n    name_ar: 'أثواب التخرج',\n    name_en: 'Graduation Gowns',\n    name_fr: 'Robes de Graduation',\n    slug: 'gown',\n    icon: '👘',\n    description: 'أثواب التخرج الأكاديمية التقليدية',\n    is_active: true,\n    order_index: 1,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    name_ar: 'قبعات التخرج',\n    name_en: 'Graduation Caps',\n    name_fr: 'Chapeaux de Graduation',\n    slug: 'cap',\n    icon: '🎩',\n    description: 'قبعات التخرج الأكاديمية',\n    is_active: true,\n    order_index: 2,\n    created_at: '2024-01-15T10:05:00Z',\n    updated_at: '2024-01-15T10:05:00Z'\n  },\n  {\n    id: '3',\n    name_ar: 'شرابات التخرج',\n    name_en: 'Graduation Tassels',\n    name_fr: 'Glands de Graduation',\n    slug: 'tassel',\n    icon: '🏷️',\n    description: 'شرابات التخرج الملونة',\n    is_active: true,\n    order_index: 3,\n    created_at: '2024-01-15T10:10:00Z',\n    updated_at: '2024-01-15T10:10:00Z'\n  },\n  {\n    id: '4',\n    name_ar: 'أوشحة التخرج',\n    name_en: 'Graduation Stoles',\n    name_fr: 'Étoles de Graduation',\n    slug: 'stole',\n    icon: '🧣',\n    description: 'أوشحة التخرج المميزة',\n    is_active: true,\n    order_index: 4,\n    created_at: '2024-01-15T10:15:00Z',\n    updated_at: '2024-01-15T10:15:00Z'\n  },\n  {\n    id: '5',\n    name_ar: 'القلانس الأكاديمية',\n    name_en: 'Academic Hoods',\n    name_fr: 'Capuches Académiques',\n    slug: 'hood',\n    icon: '🎓',\n    description: 'القلانس الأكاديمية للدرجات العليا',\n    is_active: true,\n    order_index: 5,\n    created_at: '2024-01-15T10:20:00Z',\n    updated_at: '2024-01-15T10:20:00Z'\n  }\n]\n\n// بيانات وهمية للمنتجات\nexport const mockProducts: MockProduct[] = [\n  {\n    id: '1',\n    name: 'ثوب التخرج الكلاسيكي',\n    description: 'ثوب تخرج أنيق مصنوع من أجود الخامات، مناسب لجميع المناسبات الأكاديمية',\n    category: 'gown',\n    price: 299.99,\n    rental_price: 99.99,\n    colors: ['أسود', 'أزرق داكن', 'بورجوندي'],\n    sizes: ['S', 'M', 'L', 'XL', 'XXL'],\n    images: ['/images/products/gown-classic-1.jpg', '/images/products/gown-classic-2.jpg'],\n    stock_quantity: 25,\n    is_available: true,\n    created_at: '2024-01-15T10:00:00Z',\n    updated_at: '2024-01-20T14:30:00Z',\n    rating: 4.8,\n    reviews_count: 42,\n    features: ['مقاوم للتجاعيد', 'قابل للغسيل', 'خامة عالية الجودة'],\n    specifications: {\n      material: 'بوليستر عالي الجودة',\n      weight: '0.8 كيلو',\n      care: 'غسيل جاف أو غسيل عادي'\n    }\n  },\n  {\n    id: '2',\n    name: 'قبعة التخرج التقليدية',\n    description: 'قبعة تخرج تقليدية مع شرابة ذهبية، رمز الإنجاز الأكاديمي',\n    category: 'cap',\n    price: 79.99,\n    rental_price: 29.99,\n    colors: ['أسود', 'أزرق داكن'],\n    sizes: ['One Size'],\n    images: ['/images/products/cap-traditional-1.jpg'],\n    stock_quantity: 50,\n    is_available: true,\n    created_at: '2024-01-16T09:00:00Z',\n    updated_at: '2024-01-22T16:45:00Z',\n    rating: 4.6,\n    reviews_count: 28,\n    features: ['مقاس واحد يناسب الجميع', 'شرابة ذهبية', 'تصميم تقليدي'],\n    specifications: {\n      material: 'قطن مخلوط',\n      tassel_color: 'ذهبي',\n      adjustable: 'نعم'\n    }\n  },\n  {\n    id: '3',\n    name: 'وشاح التخرج المطرز',\n    description: 'وشاح تخرج مطرز بخيوط ذهبية، يضيف لمسة من الأناقة والتميز',\n    category: 'stole',\n    price: 149.99,\n    rental_price: 49.99,\n    colors: ['أبيض مع ذهبي', 'أزرق مع فضي', 'أحمر مع ذهبي'],\n    sizes: ['One Size'],\n    images: ['/images/products/stole-embroidered-1.jpg', '/images/products/stole-embroidered-2.jpg'],\n    stock_quantity: 15,\n    is_available: true,\n    created_at: '2024-01-17T11:00:00Z',\n    updated_at: '2024-01-25T10:15:00Z',\n    rating: 4.9,\n    reviews_count: 18,\n    features: ['تطريز يدوي', 'خيوط ذهبية', 'تصميم فاخر'],\n    specifications: {\n      material: 'حرير طبيعي',\n      embroidery: 'خيوط ذهبية وفضية',\n      length: '150 سم'\n    }\n  },\n  {\n    id: '4',\n    name: 'شرابة التخرج الذهبية',\n    description: 'شرابة تخرج ذهبية اللون، رمز التفوق والإنجاز الأكاديمي',\n    category: 'tassel',\n    price: 39.99,\n    rental_price: 15.99,\n    colors: ['ذهبي', 'فضي', 'أزرق', 'أحمر'],\n    sizes: ['One Size'],\n    images: ['/images/products/tassel-gold-1.jpg'],\n    stock_quantity: 100,\n    is_available: true,\n    created_at: '2024-01-18T14:00:00Z',\n    updated_at: '2024-01-26T09:30:00Z',\n    rating: 4.7,\n    reviews_count: 35,\n    features: ['خيوط عالية الجودة', 'ألوان ثابتة', 'سهل التركيب'],\n    specifications: {\n      material: 'خيوط حريرية',\n      length: '23 سم',\n      attachment: 'مشبك معدني'\n    }\n  },\n  {\n    id: '5',\n    name: 'قلنسوة الدكتوراه الفاخرة',\n    description: 'قلنسوة دكتوراه فاخرة مصممة خصيصاً لحفلات التخرج الأكاديمية العليا',\n    category: 'hood',\n    price: 199.99,\n    rental_price: 79.99,\n    colors: ['أسود مع ذهبي', 'أزرق مع فضي'],\n    sizes: ['M', 'L', 'XL'],\n    images: ['/images/products/hood-doctorate-1.jpg', '/images/products/hood-doctorate-2.jpg'],\n    stock_quantity: 8,\n    is_available: true,\n    created_at: '2024-01-19T16:00:00Z',\n    updated_at: '2024-01-27T12:00:00Z',\n    rating: 5.0,\n    reviews_count: 12,\n    features: ['تصميم أكاديمي أصيل', 'خامة فاخرة', 'مناسب للدكتوراه'],\n    specifications: {\n      material: 'مخمل عالي الجودة',\n      lining: 'حرير ملون',\n      academic_level: 'دكتوراه'\n    }\n  }\n]\n\n// مساعدات للتعامل مع البيانات الوهمية\nexport class MockDataManager {\n  private static getStorageKey(type: 'pages' | 'menuItems' | 'products' | 'categories'): string {\n    return `mockData_${type}`\n  }\n\n  static getPages(): MockPage[] {\n    if (typeof window === 'undefined') return mockPages\n\n    const stored = localStorage.getItem(this.getStorageKey('pages'))\n    return stored ? JSON.parse(stored) : mockPages\n  }\n\n  static getMenuItems(): MockMenuItem[] {\n    if (typeof window === 'undefined') return mockMenuItems\n\n    const stored = localStorage.getItem(this.getStorageKey('menuItems'))\n    return stored ? JSON.parse(stored) : mockMenuItems\n  }\n\n  static getProducts(): MockProduct[] {\n    if (typeof window === 'undefined') return mockProducts\n\n    const stored = localStorage.getItem(this.getStorageKey('products'))\n    return stored ? JSON.parse(stored) : mockProducts\n  }\n\n  static getCategories(): MockCategory[] {\n    if (typeof window === 'undefined') return mockCategories\n\n    const stored = localStorage.getItem(this.getStorageKey('categories'))\n    return stored ? JSON.parse(stored) : mockCategories\n  }\n\n  static savePages(pages: MockPage[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('pages'), JSON.stringify(pages))\n    }\n  }\n\n  static saveMenuItems(items: MockMenuItem[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('menuItems'), JSON.stringify(items))\n    }\n  }\n\n  static saveProducts(products: MockProduct[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('products'), JSON.stringify(products))\n    }\n  }\n\n  static saveCategories(categories: MockCategory[]): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem(this.getStorageKey('categories'), JSON.stringify(categories))\n    }\n  }\n\n  static generateId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 11)\n  }\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;AA4E1B,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,cAAc;QACd,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,UAAU;YACR,WAAW;QACb;QACA,cAAc;YACZ;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,kBAAkB;gBAClB,eAAe;YACjB;SACD;IACH;CACD;AAGM,MAAM,gBAAgC;IAC3C;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,WAAW;QACX,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,UAAU;QACV,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,iBAAiC;IAC5C;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;IACd;CACD;AAGM,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAa;SAAW;QACzC,OAAO;YAAC;YAAK;YAAK;YAAK;YAAM;SAAM;QACnC,QAAQ;YAAC;YAAuC;SAAsC;QACtF,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAkB;YAAe;SAAoB;QAChE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;SAAY;QAC7B,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAyC;QAClD,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAA0B;YAAe;SAAe;QACnE,gBAAgB;YACd,UAAU;YACV,cAAc;YACd,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;YAAe;SAAe;QACvD,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;YAA4C;SAA2C;QAChG,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAc;YAAc;SAAa;QACpD,gBAAgB;YACd,UAAU;YACV,YAAY;YACZ,QAAQ;QACV;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAQ;YAAO;YAAQ;SAAO;QACvC,OAAO;YAAC;SAAW;QACnB,QAAQ;YAAC;SAAqC;QAC9C,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAqB;YAAe;SAAc;QAC7D,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,YAAY;QACd;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,OAAO;QACP,cAAc;QACd,QAAQ;YAAC;YAAgB;SAAc;QACvC,OAAO;YAAC;YAAK;YAAK;SAAK;QACvB,QAAQ;YAAC;YAAyC;SAAwC;QAC1F,gBAAgB;QAChB,cAAc;QACd,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,eAAe;QACf,UAAU;YAAC;YAAsB;YAAc;SAAkB;QACjE,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,gBAAgB;QAClB;IACF;CACD;AAGM,MAAM;IACX,OAAe,cAAc,IAAuD,EAAU;QAC5F,OAAO,CAAC,SAAS,EAAE,MAAM;IAC3B;IAEA,OAAO,WAAuB;QAC5B,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,eAA+B;QACpC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,cAA6B;QAClC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,gBAAgC;QACrC,wCAAmC,OAAO;;QAE1C,MAAM;IAER;IAEA,OAAO,UAAU,KAAiB,EAAQ;QACxC,uCAAmC;;QAEnC;IACF;IAEA,OAAO,cAAc,KAAqB,EAAQ;QAChD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAa,QAAuB,EAAQ;QACjD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,eAAe,UAA0B,EAAQ;QACtD,uCAAmC;;QAEnC;IACF;IAEA,OAAO,aAAqB;QAC1B,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACzE;AACF", "debugId": null}}, {"offset": {"line": 554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Graduation%20Toqs/frontend/src/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { MockDataManager, MockProduct } from '@/lib/mockData'\n\n// GET - جلب جميع المنتجات\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const category = searchParams.get('category')\n    const available = searchParams.get('available')\n    const limit = searchParams.get('limit')\n    const offset = searchParams.get('offset')\n\n    // جلب البيانات الوهمية\n    let products = MockDataManager.getProducts()\n\n    // تطبيق الفلاتر\n    if (category && category !== 'all') {\n      products = products.filter(product => product.category === category)\n    }\n\n    if (available === 'true') {\n      products = products.filter(product => product.is_available === true)\n    } else if (available === 'false') {\n      products = products.filter(product => product.is_available === false)\n    }\n\n    // ترتيب حسب تاريخ الإنشاء\n    products.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())\n\n    // تطبيق التصفح\n    const limitNum = limit ? parseInt(limit) : products.length\n    const offsetNum = offset ? parseInt(offset) : 0\n    \n    const paginatedProducts = products.slice(offsetNum, offsetNum + limitNum)\n\n    return NextResponse.json({ \n      products: paginatedProducts,\n      total: products.length\n    })\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST - إضافة منتج جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const {\n      name,\n      description,\n      category,\n      price,\n      rental_price,\n      colors,\n      sizes,\n      images,\n      stock_quantity,\n      is_available,\n      features,\n      specifications\n    } = body\n\n    // التحقق من البيانات المطلوبة\n    if (!name || !description || !category || !price) {\n      return NextResponse.json(\n        { error: 'البيانات المطلوبة مفقودة' },\n        { status: 400 }\n      )\n    }\n\n    // جلب المنتجات الحالية\n    const products = MockDataManager.getProducts()\n\n    // إنشاء المنتج الجديد\n    const newProduct: MockProduct = {\n      id: MockDataManager.generateId(),\n      name,\n      description,\n      category,\n      price: parseFloat(price),\n      rental_price: rental_price ? parseFloat(rental_price) : undefined,\n      colors: colors || [],\n      sizes: sizes || [],\n      images: images || [],\n      stock_quantity: parseInt(stock_quantity) || 0,\n      is_available: is_available ?? true,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n      features: features || [],\n      specifications: specifications || {}\n    }\n\n    // حفظ المنتج\n    products.push(newProduct)\n    MockDataManager.saveProducts(products)\n\n    return NextResponse.json({ \n      message: 'تم إضافة المنتج بنجاح',\n      product: newProduct \n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Unexpected error:', error)\n    return NextResponse.json(\n      { error: 'خطأ غير متوقع' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,uBAAuB;QACvB,IAAI,WAAW,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAE1C,gBAAgB;QAChB,IAAI,YAAY,aAAa,OAAO;YAClC,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;QAC7D;QAEA,IAAI,cAAc,QAAQ;YACxB,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,YAAY,KAAK;QACjE,OAAO,IAAI,cAAc,SAAS;YAChC,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,YAAY,KAAK;QACjE;QAEA,0BAA0B;QAC1B,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;QAEzF,eAAe;QACf,MAAM,WAAW,QAAQ,SAAS,SAAS,SAAS,MAAM;QAC1D,MAAM,YAAY,SAAS,SAAS,UAAU;QAE9C,MAAM,oBAAoB,SAAS,KAAK,CAAC,WAAW,YAAY;QAEhE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,UAAU;YACV,OAAO,SAAS,MAAM;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,KAAK,EACL,YAAY,EACZ,MAAM,EACN,KAAK,EACL,MAAM,EACN,cAAc,EACd,YAAY,EACZ,QAAQ,EACR,cAAc,EACf,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,OAAO;YAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,WAAW,wHAAA,CAAA,kBAAe,CAAC,WAAW;QAE5C,sBAAsB;QACtB,MAAM,aAA0B;YAC9B,IAAI,wHAAA,CAAA,kBAAe,CAAC,UAAU;YAC9B;YACA;YACA;YACA,OAAO,WAAW;YAClB,cAAc,eAAe,WAAW,gBAAgB;YACxD,QAAQ,UAAU,EAAE;YACpB,OAAO,SAAS,EAAE;YAClB,QAAQ,UAAU,EAAE;YACpB,gBAAgB,SAAS,mBAAmB;YAC5C,cAAc,gBAAgB;YAC9B,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;YAClC,UAAU,YAAY,EAAE;YACxB,gBAAgB,kBAAkB,CAAC;QACrC;QAEA,aAAa;QACb,SAAS,IAAI,CAAC;QACd,wHAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QAE7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}